// Contagem regressiva para 30 de março de 2028
$(document).ready(function() {
    // Definir a data alvo (30 de março de 2028)
    const targetDate = new Date('2028-03-30T00:00:00');
    
    // Função para calcular e atualizar a contagem regressiva
    function updateCountdown() {
        // Data atual
        const currentDate = new Date();
        
        // Diferença em milissegundos
        const difference = targetDate - currentDate;
        
        // Se a data já passou
        if (difference < 0) {
            $('#years').text('0');
            $('#months').text('0');
            $('#days').text('0');
            return;
        }
        
        // Cálculo de anos, meses e dias restantes
        let years = 0;
        let months = 0;
        let days = 0;
        
        // Clonar a data atual para cálculos
        let tempDate = new Date(currentDate);
        
        // Calcular anos
        while (true) {
            const nextYear = new Date(tempDate);
            nextYear.setFullYear(tempDate.getFullYear() + 1);
            
            if (nextYear > targetDate) {
                break;
            }
            
            tempDate = nextYear;
            years++;
        }
        
        // Calcular meses
        while (true) {
            const nextMonth = new Date(tempDate);
            nextMonth.setMonth(tempDate.getMonth() + 1);
            
            if (nextMonth > targetDate) {
                break;
            }
            
            tempDate = nextMonth;
            months++;
        }
        
        // Calcular dias
        while (true) {
            const nextDay = new Date(tempDate);
            nextDay.setDate(tempDate.getDate() + 1);
            
            if (nextDay > targetDate) {
                break;
            }
            
            tempDate = nextDay;
            days++;
        }
        
        // Atualizar os elementos HTML
        $('#years').text(years);
        $('#months').text(months);
        $('#days').text(days);
        
        // Adicionar animação aos números
        $('.countdown-item span:first-child').addClass('pulse');
        setTimeout(function() {
            $('.countdown-item span:first-child').removeClass('pulse');
        }, 1000);
    }
    
    // Atualizar a contagem regressiva imediatamente
    updateCountdown();
    
    // Atualizar a contagem regressiva a cada dia
    setInterval(updateCountdown, 86400000); // 24 horas em milissegundos
    
    // Para fins de demonstração, também atualizamos a cada minuto
    // Em produção, pode ser removido para economizar recursos
    setInterval(updateCountdown, 60000); // 1 minuto em milissegundos
});
