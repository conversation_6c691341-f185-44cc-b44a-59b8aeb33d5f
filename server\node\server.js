
const express = require('express');
const bodyParser = require('body-parser');
const cors = require('cors');
const fs = require('fs');
const path = require('path');
const https = require('https');
const http = require('http');
const createCsvWriter = require('csv-writer').createObjectCsvWriter;

// Configuração do servidor
const app = express();
const HTTP_PORT = process.env.HTTP_PORT || 80;
const HTTPS_PORT = process.env.HTTPS_PORT || 443;

// Obter caminhos dos certificados SSL das variáveis de ambiente
const SSL_KEY_PATH = process.env.SSL_KEY_PATH || '/etc/letsencrypt/live/quatrobandeiras.com.br/privkey.pem';
const SSL_CERT_PATH = process.env.SSL_CERT_PATH || '/etc/letsencrypt/live/quatrobandeiras.com.br/fullchain.pem';

// Middleware
app.use(cors({
    origin: '*', // Permite todas as origens
    methods: ['GET', 'POST', 'OPTIONS'], // Métodos permitidos
    allowedHeaders: ['Content-Type', 'Authorization'] // Headers permitidos
}));
app.use(bodyParser.json());
app.use(bodyParser.urlencoded({ extended: true }));

// Middleware para redirecionamento para HTTPS e www
app.use((req, res, next) => {
    // Verificar se estamos em ambiente de desenvolvimento
    if (process.env.NODE_ENV === 'development') {
        return next();
    }
    
    // Obter o host da requisição
    const host = req.header('host');
    
    // Se não há host header, pular o redirecionamento
    if (!host) {
        return next();
    }
    
    // Verificar se é HTTPS (req.secure) ou se a requisição veio através de um proxy HTTPS
    const isSecure = req.secure || req.header('x-forwarded-proto') === 'https';
    
    // Verificar se o host começa com www.
    const isWww = host.startsWith('www.');
    
    // Construir o novo host com www. se necessário
    let newHost = host;
    if (!isWww) {
        newHost = 'www.' + host.replace(/^www\./i, '');
    }
    
    // Aplicar redirecionamento apenas quando necessário
    if (!isSecure || !isWww) {
        // Redirecionar para HTTPS e www em um único passo
        return res.redirect(301, `https://${newHost}${req.url}`);
    }
    
    next();
});

// Diretório para armazenar o CSV
const dataDir = path.join(__dirname, '../data');
if (!fs.existsSync(dataDir)) {
    fs.mkdirSync(dataDir, { recursive: true });
}

// Caminho para o arquivo CSV
const csvFilePath = path.join(dataDir, 'interessados.csv');

// Configuração do CSV Writer
const csvWriter = createCsvWriter({
    path: csvFilePath,
    header: [
        { id: 'data', title: 'DATA' },
        { id: 'nome', title: 'NOME' },
        { id: 'email', title: 'EMAIL' },
        { id: 'telefone', title: 'TELEFONE' },
        { id: 'mensagem', title: 'MENSAGEM' }
    ],
    append: fs.existsSync(csvFilePath)
});

// IMPORTANTE: Definir as rotas de API ANTES de servir arquivos estáticos
// Endpoint para receber dados do formulário
app.post('/api/save-interest', (req, res) => {
    try {
        console.log('Recebendo requisição POST em /api/save-interest');
        console.log('Body:', req.body);
        
        // Validar campos obrigatórios
        const { name, email, phone } = req.body;
        
        if (!name || !email || !phone) {
            return res.status(400).json({
                success: false,
                message: 'Por favor, preencha todos os campos obrigatórios.'
            });
        }
        
        // Validar e-mail (validação básica)
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(email)) {
            return res.status(400).json({
                success: false,
                message: 'Por favor, informe um e-mail válido.'
            });
        }
        
        // Não validamos o formato do telefone no backend para permitir formatos internacionais
        
        // Preparar dados para CSV
        const record = {
            data: new Date().toISOString().replace('T', ' ').substring(0, 19),
            nome: name.trim(),
            email: email.trim(),
            telefone: phone.trim(),
            mensagem: (req.body.message || '').trim()
        };
        
        // Gravar no CSV
        csvWriter.writeRecords([record])
            .then(() => {
                res.json({
                    success: true,
                    message: 'Muito Obrigado! Entraremos em contato após a colheita.'
                });
            })
            .catch(error => {
                console.error('Erro ao salvar dados:', error);
                res.status(500).json({
                    success: false,
                    message: 'Erro ao salvar os dados. Por favor, tente novamente mais tarde.'
                });
            });
    } catch (error) {
        console.error('Erro no processamento do formulário:', error);
        res.status(500).json({
            success: false,
            message: 'Ocorreu um erro ao processar sua solicitação. Por favor, tente novamente.'
        });
    }
});

// Rota OPTIONS para preflight CORS
app.options('/api/save-interest', cors());

// DEPOIS das rotas de API, servir arquivos estáticos
app.use(express.static(path.join(__dirname, '../../')));

// Rota para a página principal
app.get('/', (req, res) => {
    res.sendFile(path.join(__dirname, '../../index.html'));
});

// Rota de fallback para SPA
app.get('*', (req, res) => {
    // Verificar se a rota começa com /api
    if (req.path.startsWith('/api')) {
        return res.status(404).json({
            success: false,
            message: 'API endpoint não encontrado'
        });
    }
    
    // Para outras rotas, enviar o index.html
    res.sendFile(path.join(__dirname, '../../index.html'));
});

// Definir ambiente de desenvolvimento se não estiver em produção
if (!process.env.NODE_ENV) {
    process.env.NODE_ENV = 'development';
}

// Iniciar servidor HTTP
const httpServer = http.createServer(app);
httpServer.listen(HTTP_PORT, '0.0.0.0', () => {
    console.log(`Servidor HTTP rodando na porta ${HTTP_PORT}`);
    if (process.env.NODE_ENV !== 'development') {
        console.log('Redirecionando para HTTPS');
    }
});

// Verificar se os certificados existem e se estamos em produção
if (process.env.NODE_ENV !== 'development') {
    try {
        // Iniciar servidor HTTPS
        const httpsOptions = {
            key: fs.readFileSync(SSL_KEY_PATH),
            cert: fs.readFileSync(SSL_CERT_PATH)
        };
        
        https.createServer(httpsOptions, app).listen(HTTPS_PORT, '0.0.0.0', () => {
            console.log(`Servidor HTTPS rodando na porta ${HTTPS_PORT}`);
            console.log(`Acesse: https://www.quatrobandeiras.com.br`);
        });
        console.log('Servidor HTTPS iniciado com sucesso!');
    } catch (error) {
        console.error('Erro ao iniciar servidor HTTPS:', error);
        console.log('Verifique se os certificados SSL existem e têm permissões corretas.');
        console.log('Continuando apenas com servidor HTTP para desenvolvimento.');
    }
} else {
    console.log('Ambiente de desenvolvimento detectado, ignorando configuração HTTPS');
    console.log(`Acesse: http://localhost:${HTTP_PORT}`);
}

