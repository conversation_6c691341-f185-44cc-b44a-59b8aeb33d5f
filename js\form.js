// Manipulação do formulário de interesse
$(document).ready(function() {
    // Validação e envio do formulário
    $('#interest-form').submit(function(e) {
        e.preventDefault();
        
        // Validar formulário
        if (!validateForm()) {
            return false;
        }
        
        // Obter o idioma atual
        const currentLang = $('html').attr('lang') || 'pt-BR';
        
        // Coletar dados do formulário
        const formData = {
            name: $('#name').val().trim(),
            email: $('#email').val().trim(),
            phone: $('#phone').val().trim(),
            message: $('#message').val().trim(),
            language: currentLang // Adicionar o parâmetro language
        };
        
        // Enviar dados para o servidor Node.js
        $.ajax({
            type: 'POST',
            url: '/api/save-interest',
            data: JSON.stringify(formData),
            contentType: 'application/json',
            success: function(response) {
                // Exibir mensagem de sucesso
                $('#form-message')
                    .removeClass('error')
                    .addClass('success')
                    .html('<p><i class="fas fa-check-circle"></i> ' + response.message + '</p>');
                
                // Limpar formulário
                $('#interest-form')[0].reset();
                
                // Reinicializar o campo de telefone com base no idioma
                if (currentLang === 'en-US') {
                    $('#phone').val('+');
                } else {
                    $('#phone').val('');
                }
            },
            error: function(xhr) {
                let errorMessage = 'Ocorreu um erro ao processar sua solicitação. Por favor, tente novamente.';
                
                // Tentar obter mensagem de erro da resposta
                if (xhr.responseJSON && xhr.responseJSON.message) {
                    errorMessage = xhr.responseJSON.message;
                }
                
                // Exibir mensagem de erro
                $('#form-message')
                    .removeClass('success')
                    .addClass('error')
                    .html('<p><i class="fas fa-exclamation-circle"></i> ' + errorMessage + '</p>');
            }
        });
    });
    
    // Função para validar o formulário
    function validateForm() {
        let isValid = true;
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        const phoneRegex = /^[0-9\s\-\(\)]+$/;
        const currentLang = $('html').attr('lang') || 'pt-BR';
        
        // Validar nome
        if ($('#name').val().trim() === '') {
            showError('#name', currentLang === 'pt-BR' ? 'Por favor, informe seu nome completo.' : 'Please enter your full name.');
            isValid = false;
        } else {
            removeError('#name');
        }
        
        // Validar email
        if (!emailRegex.test($('#email').val().trim())) {
            showError('#email', currentLang === 'pt-BR' ? 'Por favor, informe um e-mail válido.' : 'Please enter a valid email address.');
            isValid = false;
        } else {
            removeError('#email');
        }
        
        // Validar telefone - diferente para cada idioma
        if (currentLang === 'pt-BR') {
            // Para português, validamos o formato brasileiro
            if (!phoneRegex.test($('#phone').val().trim())) {
                showError('#phone', 'Por favor, informe um telefone válido.');
                isValid = false;
            } else {
                removeError('#phone');
            }
        } else {
            // Para inglês, apenas verificamos se não está vazio ou só com o +
            const phoneValue = $('#phone').val().trim();
            if (phoneValue === '' || phoneValue === '+') {
                showError('#phone', 'Please enter a phone number.');
                isValid = false;
            } else {
                removeError('#phone');
            }
        }
        
        return isValid;
    }
    
    // Função para mostrar erro de validação
    function showError(selector, message) {
        $(selector).addClass('error');
        
        // Verificar se já existe mensagem de erro
        if ($(selector).next('.error-message').length === 0) {
            $(selector).after('<div class="error-message">' + message + '</div>');
        } else {
            $(selector).next('.error-message').text(message);
        }
    }
    
    // Função para remover erro de validação
    function removeError(selector) {
        $(selector).removeClass('error');
        $(selector).next('.error-message').remove();
    }
    
    // Validação em tempo real
    $('#interest-form input, #interest-form textarea').on('input', function() {
        // Se o campo tem classe de erro, validar novamente
        if ($(this).hasClass('error')) {
            validateField($(this));
        }
    });
    
    // Função para validar campo individual
    function validateField($field) {
        const id = $field.attr('id');
        const value = $field.val().trim();
        const currentLang = $('html').attr('lang') || 'pt-BR';
        
        switch (id) {
            case 'name':
                if (value === '') {
                    showError('#name', currentLang === 'pt-BR' ? 'Por favor, informe seu nome completo.' : 'Please enter your full name.');
                } else {
                    removeError('#name');
                }
                break;
                
            case 'email':
                const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                if (!emailRegex.test(value)) {
                    showError('#email', currentLang === 'pt-BR' ? 'Por favor, informe um e-mail válido.' : 'Please enter a valid email address.');
                } else {
                    removeError('#email');
                }
                break;
                
            case 'phone':
                if (currentLang === 'pt-BR') {
                    const phoneRegex = /^[0-9\s\-\(\)]+$/;
                    if (!phoneRegex.test(value)) {
                        showError('#phone', 'Por favor, informe um telefone válido.');
                    } else {
                        removeError('#phone');
                    }
                } else {
                    // Para inglês, apenas verificamos se não está vazio ou só com o +
                    if (value === '' || value === '+') {
                        showError('#phone', 'Please enter a phone number.');
                    } else {
                        removeError('#phone');
                    }
                }
                break;
        }
    }
    
    // Expor a função validateForm globalmente
    window.validateForm = validateForm;
});
