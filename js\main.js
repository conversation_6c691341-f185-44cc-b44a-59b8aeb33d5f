// Arquivo principal de JavaScript para o site Quatro Bandeiras

$(document).ready(function() {
    // Menu responsivo
    $('.menu-toggle').click(function() {
        $('.menu').toggleClass('active');
    });

    // Fechar menu ao clicar em um item
    $('.menu a').click(function() {
        $('.menu').removeClass('active');
    });

    // Scroll suave para links internos
    $('a[href^="#"]').on('click', function(e) {
        e.preventDefault();
        
        var target = this.hash;
        var $target = $(target);
        
        $('html, body').animate({
            'scrollTop': $target.offset().top - 70
        }, 800, 'swing');
    });

    // Efeito de fade para elementos ao scrollar
    $(window).scroll(function() {
        var windowHeight = $(window).height();
        var scrollTop = $(window).scrollTop();
        
        $('section').each(function() {
            var offsetTop = $(this).offset().top;
            
            if (scrollTop + windowHeight > offsetTop + 100) {
                $(this).addClass('visible');
            }
        });
    });

    // Adicionar classe ao header ao scrollar
    $(window).scroll(function() {
        if ($(this).scrollTop() > 50) {
            $('header').addClass('scrolled');
        } else {
            $('header').removeClass('scrolled');
        }
    });
});
