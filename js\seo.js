// Adicionar tags SEO importantes ao site
// Incluindo meta description, keywords, canonical, robots e atualização da og:image

// Selecionar o head do documento
const head = document.querySelector('head');

// Adicionar meta description mais detalhada
if (!document.querySelector('meta[name="description"]')) {
  const metaDescription = document.createElement('meta');
  metaDescription.setAttribute('name', 'description');
  metaDescription.setAttribute('content', 'Azeite de Oliva Extravirgem Quatro Bandeiras, produzido na Serra da Mantiqueira com azeitonas cultivadas em altitude privilegiada. Produção limitada de azeite premium 100% natural.');
  head.appendChild(metaDescription);
}

// Adicionar meta keywords
if (!document.querySelector('meta[name="keywords"]')) {
  const metaKeywords = document.createElement('meta');
  metaKeywords.setAttribute('name', 'keywords');
  metaKeywords.setAttribute('content', 'azeite de oliva, extravirgem, quatro bandeiras, serra da mantiqueira, azeite premium, produção limitada, azeite brasileiro');
  head.appendChild(metaKeywords);
}

// Adicionar canonical link
if (!document.querySelector('link[rel="canonical"]')) {
  const canonicalLink = document.createElement('link');
  canonicalLink.setAttribute('rel', 'canonical');
  canonicalLink.setAttribute('href', 'https://www.quatrobandeiras.com.br/');
  head.appendChild(canonicalLink);
}

// Adicionar meta robots
if (!document.querySelector('meta[name="robots"]')) {
  const metaRobots = document.createElement('meta');
  metaRobots.setAttribute('name', 'robots');
  metaRobots.setAttribute('content', 'index, follow');
  head.appendChild(metaRobots);
}

// Atualizar og:image
const ogImage = document.querySelector('meta[property="og:image"]');
if (ogImage) {
  ogImage.setAttribute('content', 'https://www.quatrobandeiras.com.br/img/og/og_image.jpg');
} else {
  const newOgImage = document.createElement('meta');
  newOgImage.setAttribute('property', 'og:image');
  newOgImage.setAttribute('content', 'https://www.quatrobandeiras.com.br/img/og/og_image.jpg');
  head.appendChild(newOgImage);
}

// Adicionar meta tags para Twitter Card
if (!document.querySelector('meta[name="twitter:card"]')) {
  const twitterCard = document.createElement('meta');
  twitterCard.setAttribute('name', 'twitter:card');
  twitterCard.setAttribute('content', 'summary_large_image');
  head.appendChild(twitterCard);
}

if (!document.querySelector('meta[name="twitter:title"]')) {
  const twitterTitle = document.createElement('meta');
  twitterTitle.setAttribute('name', 'twitter:title');
  twitterTitle.setAttribute('content', 'Azeite Quatro Bandeiras - Extravirgem da Serra da Mantiqueira');
  head.appendChild(twitterTitle);
}

if (!document.querySelector('meta[name="twitter:description"]')) {
  const twitterDesc = document.createElement('meta');
  twitterDesc.setAttribute('name', 'twitter:description');
  twitterDesc.setAttribute('content', 'Azeite de Oliva Extravirgem produzido na Serra da Mantiqueira. Produção limitada de azeite premium 100% natural.');
  head.appendChild(twitterDesc);
}

if (!document.querySelector('meta[name="twitter:image"]')) {
  const twitterImage = document.createElement('meta');
  twitterImage.setAttribute('name', 'twitter:image');
  twitterImage.setAttribute('content', 'https://www.quatrobandeiras.com.br/img/og/og_image.jpg');
  head.appendChild(twitterImage);
}

// Adicionar meta tags para dispositivos móveis
if (!document.querySelector('meta[name="theme-color"]')) {
  const themeColor = document.createElement('meta');
  themeColor.setAttribute('name', 'theme-color');
  themeColor.setAttribute('content', '#1e3a29'); // Cor verde escura do logo
  head.appendChild(themeColor);
}

// Adicionar meta tags para localização
if (!document.querySelector('meta[name="geo.region"]')) {
  const geoRegion = document.createElement('meta');
  geoRegion.setAttribute('name', 'geo.region');
  geoRegion.setAttribute('content', 'BR-MG');
  head.appendChild(geoRegion);
}

if (!document.querySelector('meta[name="geo.placename"]')) {
  const geoPlacename = document.createElement('meta');
  geoPlacename.setAttribute('name', 'geo.placename');
  geoPlacename.setAttribute('content', 'Serra da Mantiqueira');
  head.appendChild(geoPlacename);
}

console.log('Tags SEO adicionadas com sucesso!');
