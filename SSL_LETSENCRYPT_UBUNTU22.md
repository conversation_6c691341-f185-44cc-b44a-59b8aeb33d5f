# Guia para Configuração de SSL com Let's Encrypt no Ubuntu 22.04

Este guia fornece instruções detalhadas para configurar certificados SSL usando Let's Encrypt com o prazo de validade máximo possível para seu site "Quatro Bandeiras" rodando em Node.js na porta 3000, com Nginx como proxy reverso.

## Pré-requisitos

- Ubuntu 22.04 LTS
- Nginx já instalado e configurado (usando a porta 80)
- Node.js instalado
- Um nome de domínio apontando para o servidor
- Acesso root ou sudo

## 1. Instalar o Certbot e o plugin Nginx

```bash
sudo apt update
sudo apt install -y certbot python3-certbot-nginx
```

## 2. Configurar o Nginx como proxy reverso para o Node.js

Crie um arquivo de configuração para o seu site:

```bash
sudo nano /etc/nginx/sites-available/quatro-bandeiras
```

Adicione o seguinte conteúdo (substitua `seudominio.com` pelo seu domínio real):

```nginx
server {
    listen 80;
    server_name seudominio.com www.seudominio.com;

    location / {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_cache_bypass $http_upgrade;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

Ative a configuração:

```bash
sudo ln -s /etc/nginx/sites-available/quatro-bandeiras /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl reload nginx
```

## 3. Obter certificados SSL com prazo máximo de validade

O Let's Encrypt emite certificados com validade máxima de 90 dias por padrão. Embora não seja possível aumentar este prazo (é uma limitação de segurança do Let's Encrypt), podemos configurar a renovação automática para garantir que seus certificados sejam sempre renovados antes de expirarem.

Execute o comando para obter os certificados:

```bash
sudo certbot --nginx -d seudominio.com -d www.seudominio.com
```

Durante o processo:
- Forneça seu endereço de e-mail para notificações
- Concorde com os termos de serviço
- Escolha se deseja compartilhar seu e-mail com a EFF
- Selecione a opção para redirecionar todo o tráfego HTTP para HTTPS

## 4. Verificar a configuração do Nginx após o Certbot

O Certbot modifica automaticamente sua configuração do Nginx. Verifique se está correta:

```bash
sudo cat /etc/nginx/sites-available/quatro-bandeiras
```

A configuração deve agora incluir seções para SSL, semelhante a:

```nginx
server {
    listen 80;
    server_name seudominio.com www.seudominio.com;
    return 301 https://$host$request_uri;
}

server {
    listen 443 ssl;
    server_name seudominio.com www.seudominio.com;

    ssl_certificate /etc/letsencrypt/live/seudominio.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/seudominio.com/privkey.pem;
    include /etc/letsencrypt/options-ssl-nginx.conf;
    ssl_dhparam /etc/letsencrypt/ssl-dhparams.pem;

    location / {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_cache_bypass $http_upgrade;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

## 5. Configurar o Node.js para funcionar com o proxy HTTPS

Como o Nginx está gerenciando o SSL, seu aplicativo Node.js pode continuar rodando na porta 3000 sem HTTPS direto. O arquivo `server.js` já foi ajustado para isso.

Inicie seu aplicativo Node.js:

```bash
cd /var/www/s4b/server/node
npm install
npm start
```

Para manter o aplicativo rodando permanentemente, use o PM2:

```bash
sudo npm install -g pm2
pm2 start server.js --name quatro-bandeiras
pm2 save
pm2 startup
```

## 6. Verificar a renovação automática do Let's Encrypt

O Certbot instala automaticamente um timer systemd que tentará renovar seus certificados quando estiverem próximos da expiração. Verifique se está ativo:

```bash
sudo systemctl status certbot.timer
```

Você pode testar o processo de renovação com:

```bash
sudo certbot renew --dry-run
```

## 7. Configurações adicionais para segurança máxima

### Melhorar a configuração SSL do Nginx

Edite o arquivo de configuração do Nginx:

```bash
sudo nano /etc/nginx/sites-available/quatro-bandeiras
```

Adicione estas configurações dentro do bloco `server` para HTTPS:

```nginx
# Configurações de segurança adicionais
ssl_protocols TLSv1.2 TLSv1.3;
ssl_prefer_server_ciphers on;
ssl_ciphers ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES128-GCM-SHA256:ECDHE-ECDSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-ECDSA-CHACHA20-POLY1305:ECDHE-RSA-CHACHA20-POLY1305:DHE-RSA-AES128-GCM-SHA256:DHE-RSA-AES256-GCM-SHA384;
ssl_session_timeout 1d;
ssl_session_cache shared:SSL:10m;
ssl_session_tickets off;

# HSTS (15768000 segundos = 6 meses)
add_header Strict-Transport-Security "max-age=15768000; includeSubDomains" always;

# Outras headers de segurança
add_header X-Content-Type-Options nosniff;
add_header X-Frame-Options SAMEORIGIN;
add_header X-XSS-Protection "1; mode=block";
```

Reinicie o Nginx:

```bash
sudo nginx -t
sudo systemctl reload nginx
```

## 8. Monitoramento e manutenção

### Verificar status dos certificados

```bash
sudo certbot certificates
```

### Forçar renovação manual (se necessário)

```bash
sudo certbot renew --force-renewal
```

### Verificar logs do Certbot

```bash
sudo journalctl -u certbot.service
```

## Solução de problemas

### Se a renovação automática falhar

1. Verifique os logs:
   ```bash
   sudo journalctl -u certbot.service
   ```

2. Verifique permissões:
   ```bash
   sudo chmod -R 755 /etc/letsencrypt/live
   sudo chmod -R 755 /etc/letsencrypt/archive
   ```

3. Renovação manual:
   ```bash
   sudo certbot renew --force-renewal
   ```

### Se o Nginx não iniciar após configuração SSL

1. Verifique a sintaxe:
   ```bash
   sudo nginx -t
   ```

2. Verifique os logs:
   ```bash
   sudo journalctl -u nginx.service
   ```

## Resumo

Com esta configuração:

1. Seu site "Quatro Bandeiras" roda na porta 3000 via Node.js
2. O Nginx atua como proxy reverso, gerenciando o SSL
3. Os certificados Let's Encrypt são renovados automaticamente
4. A configuração SSL é segura e otimizada
5. Todo o tráfego HTTP é redirecionado para HTTPS

Embora o Let's Encrypt limite os certificados a 90 dias, a renovação automática garante que seu site permaneça seguro sem intervenção manual.
