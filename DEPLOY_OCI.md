# Instruções para Deploy do Site Quatro Bandeiras na OCI

## Visão Geral
Este documento contém instruções detalhadas para implantar o site "Quatro Bandeiras" em uma instância da Oracle Cloud Infrastructure (OCI), configurado para funcionar nas portas 80 (HTTP) e 443 (HTTPS).

## Requisitos
- Instância OCI com Oracle Linux, Ubuntu ou outra distribuição Linux
- Node.js 14.x ou superior instalado
- Acesso root ou sudo para configurar portas privilegiadas (80 e 443)
- Certificados SSL para HTTPS (opcional, mas recomendado para produção)

## Estrutura de Arquivos
```
quatro_bandeiras_site/
│
├── index.html                  # Página principal
├── css/                        # Estilos CSS
├── js/                         # Scripts JavaScript
├── img/                        # Diretório para imagens
│
└── server/
    ├── node/                   # Backend Node.js
    │   ├── package.json        # Dependências do Node.js
    │   └── server.js           # Servidor Express configurado para portas 80/443
    └── data/                   # Diretório para armazenar o CSV
```

## Passos para Deploy

### 1. Preparação da Instância OCI

1. Acesse sua instância OCI via SSH:
   ```
   ssh usuario@seu-ip-oci
   ```

2. Instale o Node.js (se ainda não estiver instalado):
   ```
   # Para Ubuntu/Debian
   curl -fsSL https://deb.nodesource.com/setup_16.x | sudo -E bash -
   sudo apt-get install -y nodejs

   # Para Oracle Linux/RHEL
   curl -fsSL https://rpm.nodesource.com/setup_16.x | sudo bash -
   sudo yum install -y nodejs
   ```

3. Instale o PM2 para gerenciar o processo Node.js:
   ```
   sudo npm install -g pm2
   ```

### 2. Configuração do Firewall

Certifique-se de que as portas 80 e 443 estão abertas no firewall da instância:

```
# Para Ubuntu/Debian
sudo ufw allow 80/tcp
sudo ufw allow 443/tcp

# Para Oracle Linux/RHEL
sudo firewall-cmd --permanent --add-port=80/tcp
sudo firewall-cmd --permanent --add-port=443/tcp
sudo firewall-cmd --reload
```

Também verifique se as regras de segurança da OCI permitem tráfego nas portas 80 e 443:
1. No console da OCI, vá para Networking > Virtual Cloud Networks
2. Selecione sua VCN e depois a Security List associada à sua instância
3. Adicione regras de entrada para as portas 80 e 443

### 3. Upload e Instalação do Site

1. Faça upload do arquivo zip para sua instância OCI:
   ```
   scp quatro_bandeiras_site_nodejs.zip usuario@seu-ip-oci:~
   ```

2. Conecte-se à instância e extraia o arquivo:
   ```
   ssh usuario@seu-ip-oci
   unzip quatro_bandeiras_site_nodejs.zip
   ```

3. Navegue até o diretório do servidor e instale as dependências:
   ```
   cd quatro_bandeiras_site/server/node
   npm install
   ```

### 4. Configuração HTTPS (Opcional, mas Recomendado)

Para habilitar HTTPS, você precisa de certificados SSL. Você pode:

1. Usar certificados existentes:
   - Coloque seus arquivos de chave privada e certificado em um local seguro na instância

2. Gerar certificados autoassinados (apenas para testes):
   ```
   mkdir -p /etc/ssl/quatro-bandeiras
   openssl req -x509 -nodes -days 365 -newkey rsa:2048 -keyout /etc/ssl/quatro-bandeiras/private.key -out /etc/ssl/quatro-bandeiras/certificate.crt
   ```

3. Usar Let's Encrypt para certificados gratuitos (recomendado para produção):
   ```
   sudo apt-get install certbot
   sudo certbot certonly --standalone -d seudominio.com
   ```

### 5. Iniciar o Servidor

#### Opção 1: Iniciar apenas com HTTP (porta 80)

```
sudo pm2 start server.js --name quatro-bandeiras
```

#### Opção 2: Iniciar com HTTP e HTTPS (portas 80 e 443)

```
sudo SSL_KEY_PATH=/caminho/para/sua/chave.key SSL_CERT_PATH=/caminho/para/seu/certificado.crt pm2 start server.js --name quatro-bandeiras
```

Se você usou Let's Encrypt, os caminhos seriam:
```
sudo SSL_KEY_PATH=/etc/letsencrypt/live/seudominio.com/privkey.pem SSL_CERT_PATH=/etc/letsencrypt/live/seudominio.com/fullchain.pem pm2 start server.js --name quatro-bandeiras
```

### 6. Configurar Inicialização Automática

Para garantir que o servidor reinicie automaticamente após reboot:

```
sudo pm2 startup
sudo pm2 save
```

## Verificação da Instalação

1. Teste o acesso HTTP:
   ```
   curl http://localhost
   ```

2. Teste o acesso HTTPS (se configurado):
   ```
   curl -k https://localhost
   ```

3. Acesse o site pelo navegador usando o IP público da sua instância OCI:
   - http://seu-ip-oci
   - https://seu-ip-oci (se HTTPS estiver configurado)

## Manutenção

### Visualizar logs
```
pm2 logs quatro-bandeiras
```

### Reiniciar o servidor
```
pm2 restart quatro-bandeiras
```

### Parar o servidor
```
pm2 stop quatro-bandeiras
```

### Atualizar o site
Para atualizar o site, substitua os arquivos e reinicie o servidor:
```
# Após substituir os arquivos
pm2 restart quatro-bandeiras
```

## Solução de Problemas

### Erro de permissão nas portas 80/443
Se você encontrar erros de permissão ao tentar usar as portas 80/443, você pode:

1. Usar sudo para iniciar o PM2 (como mostrado acima)

2. Ou configurar o Node.js para usar portas privilegiadas sem sudo:
   ```
   sudo setcap 'cap_net_bind_service=+ep' $(which node)
   ```

### Certificados SSL não funcionando
Verifique:
1. Se os caminhos para os certificados estão corretos
2. Se os arquivos têm permissões de leitura para o usuário que executa o Node.js
3. Se os certificados são válidos e não expiraram

### Problemas de conexão
Se não conseguir acessar o site externamente:
1. Verifique se o servidor está rodando: `pm2 status`
2. Confirme as configurações de firewall da instância e da OCI
3. Verifique se o IP público está correto

## Backup dos Dados
O arquivo CSV com os dados dos interessados está localizado em:
```
quatro_bandeiras_site/server/data/interessados.csv
```

Recomenda-se fazer backup regular deste arquivo para evitar perda de dados.
