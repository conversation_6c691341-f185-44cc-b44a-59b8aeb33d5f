// Biblioteca para máscaras de input
// Implementação simplificada para telefones
(function($) {
    $.fn.phoneMask = function(options) {
        const settings = $.extend({
            // Padrão: português do Brasil
            language: 'pt-BR'
        }, options);

        return this.each(function() {
            const $input = $(this);
            
            // Configuração inicial baseada no idioma
            if (settings.language === 'en-US') {
                // Para inglês, apenas garantimos que comece com + (se estiver vazio)
                if ($input.val() === '') {
                    $input.val('+');
                } else if (!$input.val().startsWith('+')) {
                    $input.val('+' + $input.val().replace(/\+/g, ''));
                }
            } else {
                // Para português, não aplicamos nada inicialmente
                // Deixamos o campo vazio para o usuário começar a digitar
                if ($input.val() === '(') {
                    $input.val('');
                }
            }
            
            $input.on('focus', function() {
                // Quando o campo recebe foco, garantimos o formato inicial correto
                if (settings.language === 'en-US' && $input.val() === '') {
                    $input.val('+');
                }
            });
            
            $input.on('input', function() {
                let value = $input.val();
                
                if (settings.language === 'pt-BR') {
                    // Para português, aplicamos a máscara brasileira
                    // Remove todos os caracteres não numéricos para processamento
                    let digits = value.replace(/\D/g, '');
                    
                    // Se não tiver dígitos, deixa o campo vazio
                    if (digits.length === 0) {
                        $input.val('');
                        return;
                    }
                    
                    // Aplica a máscara brasileira
                    if (digits.length <= 10) {
                        // Telefone fixo: (XX) XXXX-XXXX
                        if (digits.length <= 2) {
                            $input.val('(' + digits);
                        } else if (digits.length <= 6) {
                            $input.val('(' + digits.substring(0, 2) + ') ' + digits.substring(2));
                        } else {
                            $input.val('(' + digits.substring(0, 2) + ') ' + digits.substring(2, 6) + '-' + digits.substring(6));
                        }
                    } else {
                        // Celular: (XX) 9XXXX-XXXX
                        if (digits.length <= 2) {
                            $input.val('(' + digits);
                        } else if (digits.length <= 7) {
                            $input.val('(' + digits.substring(0, 2) + ') ' + digits.substring(2));
                        } else {
                            $input.val('(' + digits.substring(0, 2) + ') ' + digits.substring(2, 7) + '-' + digits.substring(7));
                        }
                    }
                } else {
                    // Para inglês, apenas garantimos que comece com + e não aplicamos máscara
                    if (value === '') {
                        $input.val('+');
                        return;
                    }
                    
                    if (!value.startsWith('+')) {
                        // Se não começa com +, adicionamos
                        value = '+' + value.replace(/\+/g, '');
                    } else {
                        // Se já começa com +, garantimos que só tenha um +
                        if (value.indexOf('+') !== value.lastIndexOf('+')) {
                            value = '+' + value.substring(1).replace(/\+/g, '');
                        }
                    }
                    $input.val(value);
                }
            });
        });
    };
})(jQuery);

// Aplicação da máscara ao campo de telefone
$(document).ready(function() {
    // Detectar idioma atual
    const currentLanguage = $('html').attr('lang') || 'pt-BR';
    
    // Inicializar o campo de telefone com valor apropriado
    if (currentLanguage === 'en-US') {
        $('#phone').val('+');
    } else {
        $('#phone').val('');
    }
    
    // Aplicar máscara ao campo de telefone
    $('#phone').phoneMask({
        language: currentLanguage
    });
    
    // Atualizar a máscara quando o idioma mudar
    $(document).on('languageChanged', function(e, lang) {
        // Limpar o campo de telefone ao mudar o idioma para evitar conflitos de formato
        $('#phone').val(lang === 'en-US' ? '+' : '');
        
        $('#phone').phoneMask({
            language: lang
        });
    });
    
    // Sobrescrever a função de validação original do form.js
    if (typeof window.validateForm === 'function') {
        const originalValidateForm = window.validateForm;
        
        window.validateForm = function() {
            // Obter o idioma atual
            const currentLang = $('html').attr('lang') || 'pt-BR';
            
            // Em inglês, qualquer valor que comece com + é válido
            if (currentLang === 'en-US') {
                const phoneValue = $('#phone').val().trim();
                // Remover qualquer erro existente
                $('#phone').removeClass('error');
                $('#phone').next('.error-message').remove();
                
                // Verificar apenas se não está vazio
                if (phoneValue === '' || phoneValue === '+') {
                    $('#phone').addClass('error');
                    $('#phone').after('<div class="error-message">Please enter a phone number.</div>');
                    return false;
                }
            }
            
            // Chamar a função original para outros casos
            return originalValidateForm();
        };
    }
});
