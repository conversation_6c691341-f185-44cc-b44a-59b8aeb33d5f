// Implementação da lógica de alternância de idioma e tradução dinâmica
$(document).ready(function() {
    // Variável para armazenar o idioma atual
    let currentLanguage = 'pt-BR';
    
    // Função para traduzir a interface com base no idioma selecionado
    function translateInterface(language) {
        // Atualiza o idioma atual
        currentLanguage = language;
        
        // Atualiza o atributo lang do HTML
        $('html').attr('lang', language.substring(0, 2));
        
        // Atualiza os elementos com atributo data-key
        $('[data-key]').each(function() {
            const key = $(this).data('key');
            if (translations[language] && translations[language][key]) {
                $(this).text(translations[language][key]);
            }
        });
        
        // Traduz elementos específicos que não têm data-key
        
        // Hero section
        $('.hero-content h2').text(translations[language]['hero_title']);
        $('.hero-content p').text(translations[language]['hero_subtitle']);
        $('.hero-content .btn').text(translations[language]['hero_button']);
        
        // Countdown section
        $('.countdown-section h2').text(translations[language]['countdown_title']);
        $('.countdown-section > .container > p:first-of-type').text(translations[language]['countdown_description']);
        $('.countdown-item .label').each(function(index) {
            const labels = ['countdown_years', 'countdown_months', 'countdown_days'];
            $(this).text(translations[language][labels[index]]);
        });
        $('.countdown-info').text(translations[language]['countdown_info']);
        
        // About section
        $('.about-text h2').text(translations[language]['about_title']);
        $('.about-text p').each(function(index) {
            $(this).text(translations[language][`about_p${index+1}`]);
        });
        
        // Features
        $('.feature:nth-child(1) h3').text(translations[language]['feature_natural_title']);
        $('.feature:nth-child(1) p').text(translations[language]['feature_natural_desc']);
        $('.feature:nth-child(2) h3').text(translations[language]['feature_mountain_title']);
        $('.feature:nth-child(2) p').text(translations[language]['feature_mountain_desc']);
        $('.feature:nth-child(3) h3').text(translations[language]['feature_premium_title']);
        $('.feature:nth-child(3) p').text(translations[language]['feature_premium_desc']);
        $('.feature:nth-child(4) h3').text(translations[language]['feature_limited_title']);
        $('.feature:nth-child(4) p').text(translations[language]['feature_limited_desc']);
        
        // Form section
        $('.form-section h2').text(translations[language]['form_title']);
        $('.form-section > .container > p:first-of-type').text(translations[language]['form_description']);
        $('label[for="name"]').text(translations[language]['form_name']);
        $('label[for="email"]').text(translations[language]['form_email']);
        $('label[for="phone"]').text(translations[language]['form_phone']);
        $('label[for="message"]').text(translations[language]['form_message']);
        $('.form-section .btn').text(translations[language]['form_submit']);
        
        // Footer
        $('.footer-logo h3').text(translations[language]['footer_title']);
        $('.footer-logo p').text(translations[language]['footer_subtitle']);
        $('.footer-contact h4').text(translations[language]['footer_contact']);
        $('.footer-social h4').text(translations[language]['footer_social']);
        
        // Atualiza o copyright no footer (mantendo a parte dinâmica do ano)
        const currentYear = new Date().getFullYear();
        const copyrightText = `© 2023-${currentYear} Quatro Bandeiras. ${translations[language]['footer_copyright']}`;
        $('.footer-bottom p').text(copyrightText);
        
        // Limpa completamente o campo de telefone antes de aplicar a máscara
        $('#phone').val('');
        
        // Atualiza a máscara de telefone com base no idioma
        updatePhoneMask(language);
        
        // Reinicializa o valor do campo de telefone com base no idioma
        if (language === 'en-US') {
            $('#phone').val('+');
        } else {
            $('#phone').val('');
        }
        
        // Atualiza o estado visual das bandeiras
        $('.language-selector .flag').removeClass('active');
        $(`.language-selector .flag[data-lang="${language}"]`).addClass('active');
        
        // Atualiza as mensagens de validação do formulário
        window.formValidationMessages = {
            name: translations[language]['form_validation_name'],
            email: translations[language]['form_validation_email'],
            phone: translations[language]['form_validation_phone']
        };
        
        // Dispara evento de mudança de idioma para outros scripts
        $(document).trigger('languageChanged', [language]);
    }
    
    // Função para atualizar a máscara de telefone com base no idioma
    function updatePhoneMask(language) {
        // Remove a máscara atual
        $('#phone').off('input');
        
        // Aplica a máscara apropriada para o idioma
        $('#phone').phoneMask({
            language: language
        });
        
        // Atualiza a validação do telefone
        if (language === 'pt-BR') {
            // Regex para telefone brasileiro: (XX) XXXX-XXXX ou (XX) XXXXX-XXXX
            window.phoneRegex = /^\(\d{2}\)\s\d{4,5}-\d{4}$/;
        } else {
            // Regex para telefone internacional: +X XXX XXX-XXXX
            window.phoneRegex = /^\+\d{1,3}\s\d{1,3}\s\d{3,5}-\d{4}$/;
        }
    }
    
    // Evento de clique nas bandeiras para alternar o idioma
    $('.language-selector .flag').click(function() {
        const language = $(this).data('lang');
        if (language !== currentLanguage) {
            translateInterface(language);
        }
    });
    
    // Inicializa a interface com o idioma padrão (português)
    translateInterface(currentLanguage);
});
