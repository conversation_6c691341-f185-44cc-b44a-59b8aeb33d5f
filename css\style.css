/* 
   Estilo principal para o site Quatro Bandeiras
   Baseado no design system do rótulo do azeite
*/

/* Variáveis CSS */
:root {
    /* Cores do design system */
    --creme-claro: #f8f3e2;
    --verde-escuro: #1a3c32;
    --dourado: #d4af37;
    --azul-claro: #6a9e9e;
    --vinho-claro: #a05165;
    --rosa-claro: #f0b5b5;
    --verde-claro: #a3c1a3;
    --cor-montanhas: #d1c7a3;
    
    /* Espaçamentos */
    --spacing-xs: 0.5rem;
    --spacing-sm: 1rem;
    --spacing-md: 2rem;
    --spacing-lg: 3rem;
    --spacing-xl: 5rem;
    
    /* Fontes */
    --font-primary: 'Playfair Display', 'Times New Roman', serif;
    --font-secondary: 'Roboto', Arial, sans-serif;
}

/* Reset e estilos base */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
}

body {
    font-family: var(--font-secondary);
    font-size: 16px;
    line-height: 1.6;
    color: var(--verde-escuro);
    background-color: var(--creme-claro);
}

.container {
    width: 90%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--spacing-sm);
}

h1, h2, h3, h4, h5, h6 {
    font-family: var(--font-primary);
    margin-bottom: var(--spacing-sm);
    font-weight: 700;
}

h1 {
    font-size: 2.5rem;
}

h2 {
    font-size: 2rem;
    position: relative;
    padding-bottom: var(--spacing-sm);
}

h2::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 2px;
    background-color: var(--dourado);
}

h3 {
    font-size: 1.5rem;
}

p {
    margin-bottom: var(--spacing-sm);
}

a {
    color: var(--verde-escuro);
    text-decoration: none;
    transition: color 0.3s ease;
}

a:hover {
    color: var(--dourado);
}

section {
    padding: var(--spacing-lg) 0;
}

.btn {
    display: inline-block;
    background-color: var(--verde-escuro);
    color: var(--creme-claro);
    padding: 12px 24px;
    border: none;
    border-radius: 4px;
    font-family: var(--font-secondary);
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 1px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn:hover {
    background-color: var(--dourado);
    color: var(--verde-escuro);
}

/* Header */
header {
    background-color: var(--creme-claro);
    padding: var(--spacing-sm) 0;
    position: fixed;
    width: 100%;
    top: 0;
    left: 0;
    z-index: 1000;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

header .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo h1 {
    font-size: 1.8rem;
    margin: 0;
    color: var(--verde-escuro);
}

.menu {
    display: flex;
    list-style: none;
}

.menu li {
    margin-left: var(--spacing-md);
}

.menu a {
    font-weight: 500;
    text-transform: uppercase;
    font-size: 0.9rem;
    letter-spacing: 1px;
}

.menu-toggle {
    display: none;
    font-size: 1.5rem;
    cursor: pointer;
}

/* Hero Section */
.hero {
    background-image: url('../img/background.jpg');
    background-size: cover;
    background-position: center;
    background-attachment: fixed;
    height: 100vh;
    display: flex;
    align-items: center;
    text-align: center;
    position: relative;
    margin-top: 60px;
}

.hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(26, 60, 50, 0.5);
}

.hero-content {
    position: relative;
    z-index: 1;
    color: var(--creme-claro);
    max-width: 800px;
    margin: 0 auto;
}

.hero h2 {
    font-size: 3rem;
    margin-bottom: var(--spacing-sm);
}

.hero p {
    font-size: 1.2rem;
    margin-bottom: var(--spacing-md);
}

/* Bandeiras */
.bandeiras {
    display: flex;
    justify-content: center;
    margin-bottom: var(--spacing-md);
}

.bandeira {
    width: 60px;
    height: 100px;
    margin: 0 10px;
    position: relative;
}

.bandeira::before {
    content: '';
    position: absolute;
    top: 0;
    left: -5px;
    width: 3px;
    height: 120px;
    background-color: var(--dourado);
}

.bandeira.azul {
    background-color: var(--azul-claro);
    clip-path: polygon(0 0, 100% 10%, 100% 90%, 0 100%);
}

.bandeira.vinho {
    background-color: var(--vinho-claro);
    clip-path: polygon(0 0, 100% 10%, 100% 90%, 0 100%);
}

.bandeira.rosa {
    background-color: var(--rosa-claro);
    clip-path: polygon(0 0, 100% 10%, 100% 90%, 0 100%);
}

.bandeira.verde {
    background-color: var(--verde-claro);
    clip-path: polygon(0 0, 100% 10%, 100% 90%, 0 100%);
}

/* Countdown Section */
.countdown-section {
    background-color: var(--creme-claro);
    text-align: center;
    padding: var(--spacing-xl) 0;
    background-image: url('../img/background.jpg');
    background-size: cover;
    background-position: center;
    background-attachment: fixed;
    position: relative;
}

.countdown-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(248, 243, 226, 0.85);
}

.countdown-section .container {
    position: relative;
    z-index: 1;
}

.countdown {
    display: flex;
    justify-content: center;
    margin: var(--spacing-md) 0;
}

.countdown-item {
    margin: 0 var(--spacing-md);
    min-width: 120px;
}

.countdown-item span {
    display: block;
}

.countdown-item span:first-child {
    font-size: 3rem;
    font-weight: 700;
    color: var(--verde-escuro);
    background-color: rgba(211, 175, 55, 0.2);
    border-radius: 8px;
    padding: var(--spacing-sm);
    margin-bottom: var(--spacing-xs);
}

.countdown-item .label {
    font-size: 1rem;
    text-transform: uppercase;
    letter-spacing: 1px;
    color: var(--verde-escuro);
}

.countdown-info {
    max-width: 600px;
    margin: var(--spacing-md) auto;
    font-style: italic;
}

/* About Section */
.about-section {
    background-color: #fff;
    padding: var(--spacing-xl) 0;
    position: relative;
}

.about-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: url('../img/background.jpg');
    background-size: cover;
    background-position: center;
    background-attachment: fixed;
    opacity: 0.1;
    z-index: 0;
}

.about-content {
    display: flex;
    align-items: center;
    margin-bottom: var(--spacing-lg);
    position: relative;
    z-index: 1;
}

.about-text {
    flex: 1;
    padding-right: var(--spacing-md);
}

.about-image {
    flex: 1;
    display: flex;
    justify-content: center;
}

.bottle-image {
    width: 250px;
    height: 400px;
    background-image: url('../img/rotulo_quatro_bandeiras.png');
    background-size: contain;
    background-position: center;
    background-repeat: no-repeat;
}

.features {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    margin-top: var(--spacing-lg);
    position: relative;
    z-index: 1;
}

.feature {
    flex-basis: 22%;
    text-align: center;
    padding: var(--spacing-sm);
    margin-bottom: var(--spacing-md);
}

.feature i {
    font-size: 2.5rem;
    color: var(--dourado);
    margin-bottom: var(--spacing-sm);
}

.feature h3 {
    margin-bottom: var(--spacing-xs);
    font-size: 1.2rem;
}

/* Form Section */
.form-section {
    background-color: var(--creme-claro);
    padding: var(--spacing-xl) 0;
    text-align: center;
    position: relative;
}

.form-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: url('../img/background.jpg');
    background-size: cover;
    background-position: center;
    background-attachment: fixed;
    opacity: 0.15;
    z-index: 0;
}

.form-section .container {
    position: relative;
    z-index: 1;
}

#interest-form {
    max-width: 600px;
    margin: var(--spacing-md) auto;
    text-align: left;
    background-color: rgba(255, 255, 255, 0.9);
    padding: var(--spacing-md);
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.form-group {
    margin-bottom: var(--spacing-md);
}

label {
    display: block;
    margin-bottom: var(--spacing-xs);
    font-weight: 500;
}

input, textarea {
    width: 100%;
    padding: 12px;
    border: 1px solid var(--verde-escuro);
    border-radius: 4px;
    background-color: #fff;
    font-family: var(--font-secondary);
    font-size: 1rem;
}

input:focus, textarea:focus {
    outline: none;
    border-color: var(--dourado);
    box-shadow: 0 0 5px rgba(211, 175, 55, 0.5);
}

.form-message {
    margin-top: var(--spacing-md);
    padding: var(--spacing-sm);
    border-radius: 4px;
}

.form-message.success {
    background-color: rgba(163, 193, 163, 0.3);
    color: var(--verde-escuro);
}

.form-message.error {
    background-color: rgba(160, 81, 101, 0.3);
    color: var(--vinho-claro);
}

/* Footer */
footer {
    background-color: var(--verde-escuro);
    color: var(--creme-claro);
    padding: var(--spacing-lg) 0 var(--spacing-sm);
    position: relative;
    overflow: hidden;
}

footer::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: url('../img/background.jpg');
    background-size: cover;
    background-position: center;
    opacity: 0.05;
    z-index: 0;
}

.footer-content {
    display: flex;
    justify-content: space-between;
    margin-bottom: var(--spacing-lg);
    position: relative;
    z-index: 1;
}

.footer-logo, .footer-contact, .footer-social {
    flex-basis: 30%;
}

.footer-logo h3 {
    color: var(--creme-claro);
    margin-bottom: var(--spacing-xs);
}

.footer-contact h4, .footer-social h4 {
    color: var(--dourado);
    margin-bottom: var(--spacing-sm);
    font-size: 1.2rem;
}

.footer-contact p {
    margin-bottom: var(--spacing-xs);
}

.footer-contact i {
    margin-right: var(--spacing-xs);
    color: var(--dourado);
}

.social-icons {
    display: flex;
}

.social-icons a {
    display: inline-block;
    margin-right: var(--spacing-sm);
    color: var(--creme-claro);
    font-size: 1.5rem;
    transition: color 0.3s ease;
}

.social-icons a:hover {
    color: var(--dourado);
}

.footer-bottom {
    text-align: center;
    padding-top: var(--spacing-md);
    border-top: 1px solid rgba(248, 243, 226, 0.2);
    position: relative;
    z-index: 1;
}

/* Animações */
@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

.pulse {
    animation: pulse 1s ease-in-out;
}

/* Responsividade */
@media (max-width: 1023px) {
    .feature {
        flex-basis: 48%;
    }
}

@media (max-width: 767px) {
    h1 {
        font-size: 2rem;
    }
    
    h2 {
        font-size: 1.8rem;
    }
    
    .menu {
        display: none;
        position: absolute;
        top: 100%;
        left: 0;
        width: 100%;
        background-color: var(--creme-claro);
        flex-direction: column;
        padding: var(--spacing-sm) 0;
        box-shadow: 0 5px 10px rgba(0, 0, 0, 0.1);
    }
    
    .menu.active {
        display: flex;
    }
    
    .menu li {
        margin: 0;
        text-align: center;
        padding: var(--spacing-xs) 0;
    }
    
    .menu-toggle {
        display: block;
    }
    
    .hero h2 {
        font-size: 2rem;
    }
    
    .hero p {
        font-size: 1rem;
    }
    
    .bandeira {
        width: 40px;
        height: 80px;
    }
    
    .countdown {
        flex-direction: column;
    }
    
    .countdown-item {
        margin: var(--spacing-xs) 0;
    }
    
    .about-content {
        flex-direction: column;
    }
    
    .about-text {
        padding-right: 0;
        margin-bottom: var(--spacing-md);
    }
    
    .feature {
        flex-basis: 100%;
    }
    
    .footer-content {
        flex-direction: column;
    }
    
    .footer-logo, .footer-contact, .footer-social {
        margin-bottom: var(--spacing-md);
        text-align: center;
    }
    
    .social-icons {
        justify-content: center;
    }
}
