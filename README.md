# Instruções para o Site Quatro Bandeiras

## Visão Geral
Este site foi desenvolvido para o azeite extravirgem "Quatro Bandeiras", baseado no design do rótulo da garrafa. O site é responsivo, funciona em dispositivos móveis e desktop, e inclui uma contagem regressiva para a colheita (30 de março de 2028) e um formulário para registro de interessados em comprar o produto.

## Estrutura de Arquivos
```
quatro_bandeiras_site/
│
├── index.html                  # Página principal
├── css/
│   └── style.css               # Estilos principais e responsivos
│
├── js/
│   ├── main.js                 # Funções gerais
│   ├── countdown.js            # Lógica da contagem regressiva
│   └── form.js                 # Lógica do formulário
│
├── img/                        # Diretório para imagens (a ser preenchido)
│
├── server/
│   ├── node/                   # Backend Node.js
│   │   ├── package.json        # Dependências do Node.js
│   │   └── server.js           # Servidor Express e endpoint do formulário
│   └── data/                   # Diretório para armazenar o CSV
│       └── interessados.csv    # Arquivo CSV para os dados (será criado automaticamente)
│
└── README.md                   # Este arquivo de documentação
```

## Funcionalidades Principais

### 1. Design Responsivo
- O site se adapta automaticamente a diferentes tamanhos de tela
- Menu hamburger em dispositivos móveis
- Layout otimizado para smartphones, tablets e desktops

### 2. Contagem Regressiva
- Exibe anos, meses e dias até 30 de março de 2028
- Atualização automática da contagem
- Cálculo preciso considerando anos bissextos

### 3. Formulário de Interesse
- Validação em tempo real dos campos
- Feedback visual para erros de preenchimento
- Armazenamento dos dados em arquivo CSV no servidor
- Confirmação visual após envio bem-sucedido

## Requisitos para Hospedagem
- Servidor com Node.js instalado (versão 14.x ou superior)
- Não requer banco de dados

## Instruções de Instalação
1. Faça upload de todos os arquivos para o diretório raiz do seu servidor web
2. Navegue até o diretório `server/node` e execute:
   ```
   npm install
   npm start
   ```
3. O servidor Node.js iniciará na porta 3000 (ou na porta definida pela variável de ambiente PORT)
4. Para manter o servidor rodando permanentemente, recomenda-se usar PM2:
   ```
   npm install -g pm2
   pm2 start server.js --name quatro-bandeiras
   ```

## Personalizações Recomendadas
1. **Imagens**:
   - Adicione uma imagem de fundo com oliveiras em terreno inclinado para a seção hero (`img/background.jpg`)
   - Adicione uma imagem da garrafa com o rótulo para a seção "Sobre" (`img/bottle.jpg`)
   - Crie um favicon personalizado (`img/favicon.ico`)

2. **Informações de Contato**:
   - Atualize os dados de contato no rodapé (e-mail, telefone)
   - Adicione links para redes sociais reais

3. **Textos**:
   - Personalize os textos descritivos sobre o produto conforme necessário

## Manutenção
- O arquivo CSV com os interessados estará disponível em `server/data/interessados.csv`
- Recomenda-se fazer backup periódico deste arquivo
- Para exportar os dados para Excel ou outro software, basta baixar o arquivo CSV

## Suporte Técnico
Para quaisquer dúvidas ou suporte técnico, entre em contato através do e-mail [<EMAIL>].
